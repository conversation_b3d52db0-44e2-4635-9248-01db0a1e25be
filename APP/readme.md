Expo React Native App - PRD and Tech Stack

README Overview

This document serves as the Product Requirements Document (PRD) and technical guide for building a mobile application version of the existing website using Expo (React Native). The app will connect to the existing Supabase backend and database, without creating new tables. The application is tailored for students and will include features for productivity, collaboration, and test analysis.

TAKE ALL THE ENV VARIABLES AND SUPABASE CONFIG from /WEB .

The app will have a beautiful and aesthetic UI, designed with a minimal, modern interface to enhance user focus and engagement. Design will follow consistent spacing, color palette (light/dark themes), smooth animations, and intuitive interactions.

Key Features

1. Study Timer

Stopwatch and Pomodoro modes

Customizable session and break durations

Local notifications for session transitions

Visually pleasing timer ring with progress animation

Vibrant colors to indicate focus and break periods

2. Study Analytics

Visual dashboards showing time spent, session types

Weekly/daily breakdown

Charts with gradient fills and smooth curves

Utilizes existing backend data structure

3. Groups for Friends

Join/Create study groups

View group members and shared goals

Real-time updates using Supabase's existing setup

Clean group cards with avatars, badges for roles

4. Mock Test Analysis

Import test data (CSV/JSON via file picker)

Generate performance reports based on existing database schema

Beautiful score graphs, radar charts for strength analysis

Topic tags with color-coded performance levels

5. Task Management

Kanban View: Drag-and-drop tasks across stages with smooth transitions

Table View: Grid layout with sorting/filtering

Task cards with priority labels, checkmarks, and subtle shadows

Real-time sync with Supabase

Technical Requirements

Functional Requirements

Authentication via Supabase (email/password, OAuth)

Timers must persist on app minimization or lock

File upload using Supabase Storage

Real-time sync for groups and tasks

Non-Functional Requirements

Smooth animations and fast load times

Secure data transactions with JWT

App should work across both Android and iOS via Expo

Dark mode support

UI consistency across screen sizes (responsive design)

Technical Stack

Framework & Libraries

React Native with Expo

React Navigation for screen routing

Zustand or React Context for state management

shadcn/ui (customized for mobile) or React Native Paper for elegant UI components

The App should heavily use google's Material 3 library for beautiful UI

Victory Native or Recharts for charts and graphs

Framer Motion (Reanimated) for beautiful transitions and gestures

AsyncStorage for local persistence (timer state)

Expo Notifications API for local push alerts

Backend & Services

Supabase (existing database and schema to be reused)

Supabase Auth for user authentication

Supabase Realtime for live updates (tasks, groups)

Supabase Storage for uploading test files

DevOps & CI/CD

GitHub Actions for automated testing and build

Expo EAS for app builds and OTA updates

Project Directory Structure (Suggested)

/src
  /components       # Reusable styled UI blocks
  /screens          # Screen views (Home, Timer, Groups, etc.)
  /services         # API calls and Supabase integrations
  /hooks            # Custom logic hooks
  /context          # Global state/context
  /utils            # Utility functions
  /assets           # Images, icons, fonts

Setup & Getting Started

Clone the repo

Install dependencies with npm install

Configure .env with Supabase credentials

Run app with npx expo start

Use Expo Go for live preview on mobile

Future Enhancements

In-app chat in study groups

Offline-first study data tracking

Custom themes and widgets

Last Updated: June 16, 2025

