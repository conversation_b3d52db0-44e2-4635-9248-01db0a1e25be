# IsotopeAI Mobile App - React Native Implementation Guide

## Overview

This document provides a comprehensive implementation guide for building the IsotopeAI mobile application using Expo (React Native). The app replicates the functionality of the existing web application with identical Supabase integration and beautiful Material 3 design.

**Important**: Copy all environment variables and Supabase configuration from `/WEB/` directory.

## Architecture Overview

The application uses a modern, streamlined approach:
- **Backend**: Supabase (Authentication, Database, Real-time subscriptions, Storage)
- **Frontend**: React Native with TypeScript, Zustand for state management
- **UI**: Material 3 Design System with React Native Paper
- **Animations**: React Native Reanimated 3 with <PERSON>tie for micro-interactions

The mobile app maintains identical data structures and API patterns for seamless synchronization.

## Core Features Implementation

### 1. Study Timer System
**Database Schema**: `study_sessions` table in Supabase
```typescript
interface StudySession {
  id: string;
  user_id: string;
  subject: string;
  task_name: string;
  task_type: string; // Lecture, Exercise, Reading, etc.
  start_time: string; // ISO timestamp
  end_time: string;
  duration: number; // seconds
  mode: 'pomodoro' | 'stopwatch';
  phase: 'work' | 'shortBreak' | 'longBreak' | 'pause';
  completed: boolean;
  date: string; // YYYY-MM-DD
  notes: string;
  productivity_rating: number; // 1-5 scale
}
```

**Key Features**:
- Pomodoro (25min work, 5min break, 15min long break after 4 sessions)
- Stopwatch mode with customizable notification intervals
- Background timer persistence using AsyncStorage
- Local notifications for session transitions
- Session pause/resume with duration tracking
- Subject selection with color-coded organization
- Task type categorization (Study, Lecture, Exercise, Reading, etc.)
- Post-session feedback and productivity rating

### 2. Subject Management System
**Database Schema**: `userSubjects` table in Supabase
```typescript
interface Subject {
  id: string;
  user_id: string;
  name: string;
  color: string; // Hex color code
  created_at: string;
  updated_at: string;
}
```

**Features**:
- Color-coded subject organization
- Predefined color palette + custom color picker
- Subject CRUD operations with real-time sync
- Integration with timer and analytics

### 3. Task Management (Kanban + Table Views)
**Database Schema**: `todos` table in Supabase
```typescript
interface TodoItem {
  id: string;
  user_id: string;
  group_id?: string;
  title: string;
  description?: string;
  status: 'todo' | 'inProgress' | 'done';
  priority: 'low' | 'medium' | 'high';
  column_id: string; // For Kanban organization
  due_date?: string;
  assigned_to?: string;
  assigned_to_photo_url?: string;
  created_at: number;
  updated_at: number;
}
```

**Features**:
- Drag-and-drop Kanban board
- Real-time collaboration via Supabase subscriptions
- Priority levels with color coding
- Due date management
- Assignment to group members
- Table view with sorting/filtering

### 4. Study Analytics Dashboard
**Data Sources**:
- `study_sessions` table for timer data
- `users` table for daily targets and preferences
- Real-time calculations for streaks and achievements

**Features**:
- Daily/weekly/monthly study time breakdowns
- Subject-wise time distribution with color-coded charts
- Productivity rating trends
- Study streak tracking
- Goal setting and progress monitoring
- Pomodoro vs Stopwatch usage analytics

### 5. Groups & Collaboration
**Database Schema**: `groups` table in Supabase
```typescript
interface Group {
  id: string;
  name: string;
  description?: string;
  created_by: string;
  members: string[]; // Array of user IDs
  is_public: boolean;
  invite_code?: string;
  created_at: string;
  updated_at: string;
}
```

**Features**:
- Create/join study groups with invite codes
- Real-time member activity tracking
- Shared task boards
- Group study sessions (timer sync)
- Member role management

### 6. Mock Test Analysis
**Database Schema**: `mock_tests` table in Supabase
- CSV/JSON file upload via Supabase Storage
- Performance analytics with radar charts
- Topic-wise strength/weakness analysis
- Score progression tracking
- Comparison with peer performance

## Technical Implementation Requirements

### Authentication System
- **Primary**: Supabase Auth (email/password, Google OAuth)
- **Session Management**: JWT tokens with auto-refresh
- **Profile Management**: User preferences, subjects, daily targets

### Data Persistence Strategy
- **Real-time Data**: Supabase subscriptions for live updates
- **Local Storage**: AsyncStorage for timer state, user preferences
- **Background Sync**: Queue failed operations for retry when online
- **Offline Support**: Cache critical data for offline timer functionality

### Performance Requirements
- **Timer Accuracy**: Background execution with WorkManager (Android) / Background App Refresh (iOS)
- **Real-time Updates**: <500ms latency for collaborative features
- **App Launch**: <2s cold start time
- **Smooth Animations**: 60fps for all transitions and gestures

## Technology Stack

### Core Framework
```json
{
  "expo": "~51.0.0",
  "react-native": "0.74.x",
  "typescript": "^5.3.0"
}
```

### Navigation & State Management
```json
{
  "@react-navigation/native": "^6.1.0",
  "@react-navigation/stack": "^6.3.0",
  "@react-navigation/bottom-tabs": "^6.5.0",
  "zustand": "^4.4.0",
  "@tanstack/react-query": "^5.0.0"
}
```

### Backend Integration
```json
{
  "@supabase/supabase-js": "^2.47.0",
  "@react-native-async-storage/async-storage": "^1.19.0"
}
```

### Material 3 UI & Styling
```json
{
  "react-native-paper": "^5.11.0",
  "react-native-vector-icons": "^10.0.0",
  "react-native-svg": "^13.14.0",
  "react-native-linear-gradient": "^2.8.0",
  "@react-native-material/core": "^1.3.7",
  "react-native-material-you": "^1.2.0",
  "react-native-dynamic-color": "^1.0.0"
}
```

### Charts & Animations
```json
{
  "victory-native": "^36.8.0",
  "react-native-reanimated": "^3.5.0",
  "react-native-gesture-handler": "^2.13.0",
  "lottie-react-native": "^6.4.0",
  "react-native-shared-element": "^0.8.4",
  "react-native-super-grid": "^4.9.6"
}
```

## Material 3 Design Implementation

### Color System & Theming
```typescript
// constants/materialTheme.ts
import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#6750A4',
    onPrimary: '#FFFFFF',
    primaryContainer: '#EADDFF',
    onPrimaryContainer: '#21005D',
    secondary: '#625B71',
    onSecondary: '#FFFFFF',
    secondaryContainer: '#E8DEF8',
    onSecondaryContainer: '#1D192B',
    tertiary: '#7D5260',
    onTertiary: '#FFFFFF',
    tertiaryContainer: '#FFD8E4',
    onTertiaryContainer: '#31111D',
    surface: '#FEF7FF',
    onSurface: '#1D1B20',
    surfaceVariant: '#E7E0EC',
    onSurfaceVariant: '#49454F',
    outline: '#79747E',
    outlineVariant: '#CAC4D0',
  }
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#D0BCFF',
    onPrimary: '#381E72',
    primaryContainer: '#4F378B',
    onPrimaryContainer: '#EADDFF',
    secondary: '#CCC2DC',
    onSecondary: '#332D41',
    secondaryContainer: '#4A4458',
    onSecondaryContainer: '#E8DEF8',
    tertiary: '#EFB8C8',
    onTertiary: '#492532',
    tertiaryContainer: '#633B48',
    onTertiaryContainer: '#FFD8E4',
    surface: '#141218',
    onSurface: '#E6E0E9',
    surfaceVariant: '#49454F',
    onSurfaceVariant: '#CAC4D0',
    outline: '#938F99',
    outlineVariant: '#49454F',
  }
};
```

### Notifications & Background Tasks
```json
{
  "expo-notifications": "~0.25.0",
  "expo-background-fetch": "~12.0.0",
  "expo-task-manager": "~11.6.0",
  "@react-native-community/push-notification-ios": "^1.11.0"
}
```

### File Handling & Storage
```json
{
  "expo-document-picker": "~11.7.0",
  "expo-file-system": "~16.0.0",
  "react-native-fs": "^2.20.0"
}
```

## Project Structure

```
src/
├── components/           # Reusable UI components
│   ├── timer/           # Timer-related components
│   ├── analytics/       # Chart and dashboard components
│   ├── tasks/           # Kanban and task management
│   ├── groups/          # Group collaboration components
│   └── common/          # Shared UI elements
├── screens/             # Screen components
│   ├── TimerScreen.tsx
│   ├── AnalyticsScreen.tsx
│   ├── TasksScreen.tsx
│   ├── GroupsScreen.tsx
│   └── ProfileScreen.tsx
├── services/            # API and backend integration
│   ├── supabase/        # Supabase client and utilities
│   ├── firebase/        # Firebase integration (legacy)
│   └── notifications/   # Push notification service
├── stores/              # Zustand state management
│   ├── authStore.ts
│   ├── timerStore.ts
│   ├── tasksStore.ts
│   └── analyticsStore.ts
├── hooks/               # Custom React hooks
│   ├── useTimer.ts
│   ├── useSupabase.ts
│   └── useNotifications.ts
├── utils/               # Utility functions
│   ├── dateUtils.ts
│   ├── timerUtils.ts
│   └── validationUtils.ts
├── types/               # TypeScript type definitions
│   ├── database.ts
│   ├── timer.ts
│   └── user.ts
└── constants/           # App constants and configuration
    ├── colors.ts
    ├── subjects.ts
    └── config.ts
```

### Material 3 Component Examples

#### Elevated Timer Card
```typescript
// components/timer/TimerCard.tsx
import { Card, Text, useTheme } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  interpolateColor
} from 'react-native-reanimated';

const TimerCard = ({ isRunning, timeRemaining }) => {
  const theme = useTheme();
  const scale = useSharedValue(1);
  const progress = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: withSpring(scale.value) }],
    backgroundColor: interpolateColor(
      progress.value,
      [0, 1],
      [theme.colors.surface, theme.colors.primaryContainer]
    ),
  }));

  return (
    <Animated.View style={animatedStyle}>
      <Card mode="elevated" style={{ margin: 16 }}>
        <Card.Content>
          <Text variant="displayLarge" style={{ textAlign: 'center' }}>
            {formatTime(timeRemaining)}
          </Text>
        </Card.Content>
      </Card>
    </Animated.View>
  );
};
```

#### Floating Action Button with Morphing
```typescript
// components/common/MorphingFAB.tsx
import { FAB } from 'react-native-paper';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing
} from 'react-native-reanimated';

const MorphingFAB = ({ icon, onPress, extended = false }) => {
  const width = useSharedValue(56);

  const animatedStyle = useAnimatedStyle(() => ({
    width: withTiming(width.value, {
      duration: 300,
      easing: Easing.bezier(0.4, 0, 0.2, 1),
    }),
  }));

  return (
    <Animated.View style={animatedStyle}>
      <FAB
        icon={icon}
        mode="elevated"
        onPress={onPress}
        style={{
          position: 'absolute',
          bottom: 16,
          right: 16,
        }}
      />
    </Animated.View>
  );
};
```

## Environment Configuration

Copy these environment variables from `/WEB/.env`:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://pcfrgvhigvklersufktf.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_URL=https://api.isotopeai.com

# Material 3 Dynamic Colors (Android 12+)
EXPO_PUBLIC_ENABLE_DYNAMIC_COLORS=true
EXPO_PUBLIC_FALLBACK_SEED_COLOR=#6750A4
```

## Key Implementation Patterns

### 1. Timer State Management
```typescript
// stores/timerStore.ts
interface TimerState {
  status: 'idle' | 'running' | 'paused';
  mode: 'pomodoro' | 'stopwatch';
  currentPhase: 'work' | 'shortBreak' | 'longBreak';
  displayTime: number;
  selectedSubject: Subject | null;
  sessionStartTime: number | null;
  completedSessions: number;
}
```

### 2. Supabase Integration Pattern
```typescript
// services/supabase/client.ts
import { createClient } from '@supabase/supabase-js';
import { Database } from '../../types/database';

export const supabase = createClient<Database>(
  process.env.EXPO_PUBLIC_SUPABASE_URL!,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!
);
```

### 3. Real-time Subscriptions
```typescript
// hooks/useSupabaseSubscription.ts
export const useTasksSubscription = (userId: string) => {
  useEffect(() => {
    const subscription = supabase
      .channel('todos')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'todos' },
        (payload) => {
          // Handle real-time updates
        }
      )
      .subscribe();

    return () => subscription.unsubscribe();
  }, [userId]);
};
```

## Development Workflow

## Animation Patterns

### 1. Shared Element Transitions
```typescript
// components/animations/SharedElementTransition.tsx
import { SharedElement } from 'react-native-shared-element';

const SubjectCard = ({ subject, onPress }) => (
  <TouchableOpacity onPress={() => onPress(subject)}>
    <SharedElement id={`subject.${subject.id}.card`}>
      <Card style={{ backgroundColor: subject.color }}>
        <Text>{subject.name}</Text>
      </Card>
    </SharedElement>
  </TouchableOpacity>
);
```

### 2. Micro-interactions with Lottie
```typescript
// components/animations/SuccessAnimation.tsx
import LottieView from 'lottie-react-native';

const SuccessAnimation = ({ visible, onComplete }) => {
  if (!visible) return null;

  return (
    <LottieView
      source={require('../../assets/animations/success.json')}
      autoPlay
      loop={false}
      onAnimationFinish={onComplete}
      style={{ width: 200, height: 200 }}
    />
  );
};
```

### 3. Gesture-based Interactions
```typescript
// components/timer/SwipeableTimer.tsx
import { PanGestureHandler } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  runOnJS,
  withSpring
} from 'react-native-reanimated';

const SwipeableTimer = ({ onSwipeUp, onSwipeDown }) => {
  const translateY = useSharedValue(0);

  const gestureHandler = useAnimatedGestureHandler({
    onStart: (_, context) => {
      context.startY = translateY.value;
    },
    onActive: (event, context) => {
      translateY.value = context.startY + event.translationY;
    },
    onEnd: (event) => {
      if (event.translationY < -50) {
        runOnJS(onSwipeUp)();
      } else if (event.translationY > 50) {
        runOnJS(onSwipeDown)();
      }
      translateY.value = withSpring(0);
    },
  });

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={animatedStyle}>
        {/* Timer content */}
      </Animated.View>
    </PanGestureHandler>
  );
};
```

## Development Workflow

### 1. Setup
```bash
npx create-expo-app IsotopeAI --template
cd IsotopeAI
npm install
```

### 2. Install Dependencies
```bash
# Core dependencies
npm install @supabase/supabase-js zustand @tanstack/react-query

# Material 3 UI and Navigation
npm install react-native-paper @react-navigation/native @react-navigation/stack
npm install @react-native-material/core react-native-vector-icons

# Animations and Charts
npm install react-native-reanimated react-native-gesture-handler
npm install victory-native lottie-react-native react-native-shared-element

# Background tasks and notifications
npx expo install expo-notifications expo-background-fetch expo-task-manager
```

### 3. Development Commands
```bash
# Start development server
npx expo start

# Run on specific platform
npx expo start --ios
npx expo start --android

# Build for production
npx expo build:ios
npx expo build:android
```

## Testing Strategy

### Unit Tests
- Timer logic and calculations
- Data transformation utilities
- Validation functions

### Integration Tests
- Supabase CRUD operations
- Real-time subscription handling
- Authentication flows

### E2E Tests
- Complete user workflows
- Timer functionality across app states
- Offline/online synchronization

## Deployment

### Development
- Use Expo Go for rapid testing
- EAS Build for internal testing builds

### Production
- EAS Build for app store builds
- Over-the-air updates for non-native changes
- App Store Connect / Google Play Console deployment

---

**Last Updated**: June 16, 2025
**Version**: 1.0.0
**Target Platforms**: iOS 13+, Android 8+ (API 26+)

